#!/usr/bin/env python3
"""
Generate placeholder 16x16 pixel art assets for the dungeon crawler game.
Creates simple colored squares and basic shapes for sprites, tiles, and items.
"""

from PIL import Image, ImageDraw
import os

def create_directories():
    """Create necessary asset directories."""
    dirs = [
        'assets/sprites',
        'assets/tiles', 
        'assets/items',
        'assets/sounds',
        'assets/music',
        'assets/ui',
        'assets/particles',
        'assets/fonts'
    ]
    
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"Created directory: {dir_path}")

def create_sprite(name, color, shape='square', size=(16, 16)):
    """Create a simple 16x16 sprite with the given color and shape."""
    img = Image.new('RGBA', size, (0, 0, 0, 0))  # Transparent background
    draw = ImageDraw.Draw(img)
    
    if shape == 'square':
        # Simple filled rectangle
        draw.rectangle([2, 2, size[0]-3, size[1]-3], fill=color, outline=(0, 0, 0, 255))
    elif shape == 'circle':
        # Simple filled circle
        draw.ellipse([2, 2, size[0]-3, size[1]-3], fill=color, outline=(0, 0, 0, 255))
    elif shape == 'triangle':
        # Simple triangle
        points = [(size[0]//2, 2), (2, size[1]-3), (size[0]-3, size[1]-3)]
        draw.polygon(points, fill=color, outline=(0, 0, 0, 255))
    elif shape == 'diamond':
        # Diamond shape
        points = [(size[0]//2, 2), (size[0]-3, size[1]//2), (size[0]//2, size[1]-3), (2, size[1]//2)]
        draw.polygon(points, fill=color, outline=(0, 0, 0, 255))
    
    return img

def create_animated_sprite(name, colors, shape='square', frames=4):
    """Create an animated sprite with multiple frames."""
    sprites = []
    for i, color in enumerate(colors):
        # Slightly vary the shape for animation effect
        if shape == 'square':
            offset = i % 2
            img = Image.new('RGBA', (16, 16), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            draw.rectangle([2+offset, 2+offset, 13-offset, 13-offset], fill=color, outline=(0, 0, 0, 255))
        else:
            img = create_sprite(f"{name}_{i}", color, shape)
        sprites.append(img)
    
    # Create a horizontal sprite sheet
    sheet_width = 16 * frames
    sheet = Image.new('RGBA', (sheet_width, 16), (0, 0, 0, 0))
    
    for i, sprite in enumerate(sprites):
        sheet.paste(sprite, (i * 16, 0))
    
    return sheet

def generate_all_assets():
    """Generate all required game assets."""
    print("Generating game assets...")
    
    # Create directories
    create_directories()
    
    # Player sprites (blue character)
    player_colors = [(100, 150, 255, 255), (120, 170, 255, 255), (100, 150, 255, 255), (80, 130, 255, 255)]
    player_sheet = create_animated_sprite('player', player_colors, 'square', 4)
    player_sheet.save('assets/sprites/player.png')
    print("Created: assets/sprites/player.png")
    
    # Enemy sprites
    enemies = [
        ('goblin', [(100, 255, 100, 255), (120, 255, 120, 255), (100, 255, 100, 255), (80, 255, 80, 255)]),
        ('skeleton', [(220, 220, 220, 255), (240, 240, 240, 255), (220, 220, 220, 255), (200, 200, 200, 255)]),
        ('orc', [(150, 100, 100, 255), (170, 120, 120, 255), (150, 100, 100, 255), (130, 80, 80, 255)]),
    ]
    
    for enemy_name, colors in enemies:
        enemy_sheet = create_animated_sprite(enemy_name, colors, 'square', 4)
        enemy_sheet.save(f'assets/sprites/{enemy_name}.png')
        print(f"Created: assets/sprites/{enemy_name}.png")
    
    # Tile sprites
    tiles = [
        ('wall', (100, 100, 100, 255), 'square'),
        ('floor', (200, 180, 140, 255), 'square'),
        ('door', (139, 69, 19, 255), 'square'),
    ]
    
    for tile_name, color, shape in tiles:
        tile_img = create_sprite(tile_name, color, shape)
        tile_img.save(f'assets/tiles/{tile_name}.png')
        print(f"Created: assets/tiles/{tile_name}.png")
    
    # Item sprites
    items = [
        ('sword', (192, 192, 192, 255), 'triangle'),
        ('armor', (139, 69, 19, 255), 'square'),
        ('potion', (255, 100, 100, 255), 'circle'),
    ]
    
    for item_name, color, shape in items:
        item_img = create_sprite(item_name, color, shape)
        item_img.save(f'assets/items/{item_name}.png')
        print(f"Created: assets/items/{item_name}.png")
    
    # UI elements
    ui_elements = [
        ('button_normal', (70, 130, 180, 255)),
        ('button_hover', (100, 160, 210, 255)),
        ('button_pressed', (50, 110, 160, 255)),
        ('health_bar_bg', (50, 50, 50, 255)),
        ('health_bar_fill', (220, 20, 60, 255)),
        ('mana_bar_bg', (50, 50, 50, 255)),
        ('mana_bar_fill', (30, 144, 255, 255)),
        ('inventory_slot', (80, 80, 80, 255)),
        ('inventory_slot_selected', (120, 120, 120, 255)),
    ]
    
    for ui_name, color in ui_elements:
        ui_img = create_sprite(ui_name, color, 'square')
        ui_img.save(f'assets/ui/{ui_name}.png')
        print(f"Created: assets/ui/{ui_name}.png")
    
    # Particle effects
    particles = [
        ('blood', (200, 0, 0, 255), 'circle', (8, 8)),
        ('sparkle', (255, 255, 100, 255), 'diamond', (8, 8)),
        ('dust', (160, 140, 120, 255), 'circle', (12, 12)),
    ]
    
    for particle_name, color, shape, size in particles:
        # Create multiple frames for particles
        frames = []
        for i in range(4 if particle_name == 'blood' else 6 if particle_name == 'sparkle' else 3):
            alpha = max(50, 255 - i * 40)  # Fade out effect
            particle_color = color[:3] + (alpha,)
            frame = create_sprite(f"{particle_name}_{i}", particle_color, shape, size)
            frames.append(frame)
        
        # Create sprite sheet
        sheet_width = size[0] * len(frames)
        sheet = Image.new('RGBA', (sheet_width, size[1]), (0, 0, 0, 0))
        for i, frame in enumerate(frames):
            sheet.paste(frame, (i * size[0], 0))
        
        sheet.save(f'assets/particles/{particle_name}.png')
        print(f"Created: assets/particles/{particle_name}.png")
    
    # Create a simple game atlas (8x8 grid of 16x16 tiles)
    atlas = Image.new('RGBA', (128, 128), (0, 0, 0, 0))
    
    # Add some basic tiles to the atlas
    basic_tiles = [
        (100, 100, 100, 255),  # Wall
        (200, 180, 140, 255),  # Floor
        (139, 69, 19, 255),    # Door
        (100, 150, 255, 255),  # Player
        (100, 255, 100, 255),  # Goblin
        (220, 220, 220, 255),  # Skeleton
        (192, 192, 192, 255),  # Sword
        (255, 100, 100, 255),  # Potion
    ]
    
    for i, color in enumerate(basic_tiles):
        x = (i % 8) * 16
        y = (i // 8) * 16
        tile = create_sprite(f"atlas_tile_{i}", color, 'square')
        atlas.paste(tile, (x, y))
    
    atlas.save('assets/sprites/game_atlas.png')
    print("Created: assets/sprites/game_atlas.png")
    
    # Create atlas JSON data
    atlas_data = {
        "frames": {},
        "meta": {
            "image": "game_atlas.png",
            "format": "RGBA8888",
            "size": {"w": 128, "h": 128},
            "scale": "1"
        }
    }
    
    tile_names = ["wall", "floor", "door", "player", "goblin", "skeleton", "sword", "potion"]
    for i, name in enumerate(tile_names):
        x = (i % 8) * 16
        y = (i // 8) * 16
        atlas_data["frames"][name] = {
            "frame": {"x": x, "y": y, "w": 16, "h": 16},
            "rotated": False,
            "trimmed": False,
            "spriteSourceSize": {"x": 0, "y": 0, "w": 16, "h": 16},
            "sourceSize": {"w": 16, "h": 16}
        }
    
    import json
    with open('assets/sprites/game_atlas.json', 'w') as f:
        json.dump(atlas_data, f, indent=2)
    print("Created: assets/sprites/game_atlas.json")
    
    print("\nAll assets generated successfully!")
    print("Note: Sound and music files need to be added separately.")

if __name__ == "__main__":
    try:
        generate_all_assets()
    except ImportError:
        print("PIL (Pillow) is required to generate images.")
        print("Install it with: pip install Pillow")
    except Exception as e:
        print(f"Error generating assets: {e}")
