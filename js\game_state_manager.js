/**
 * GAME STATE MANAGER
 * 
 * Manages different game states (menu, playing, paused, game over)
 * and handles transitions between them.
 */

class GameStateManager {
    constructor() {
        this.currentState = 'loading';
        this.previousState = null;
        this.states = new Map();
        this.transitionCallbacks = new Map();
        
        // Initialize default states
        this.initializeStates();
    }
    
    initializeStates() {
        // Loading state
        this.addState('loading', {
            enter: () => {
                console.log('Entering loading state');
                this.showLoadingScreen();
            },
            update: (deltaTime) => {
                this.updateLoadingScreen(deltaTime);
            },
            render: (renderer) => {
                this.renderLoadingScreen(renderer);
            },
            exit: () => {
                this.hideLoadingScreen();
            }
        });
        
        // Menu state
        this.addState('menu', {
            enter: () => {
                console.log('Entering menu state');
                this.showMainMenu();
            },
            update: (deltaTime) => {
                this.updateMainMenu(deltaTime);
            },
            render: (renderer) => {
                this.renderMainMenu(renderer);
            },
            exit: () => {
                this.hideMainMenu();
            }
        });
        
        // Playing state
        this.addState('playing', {
            enter: () => {
                console.log('Entering playing state');
                if (window.game) {
                    window.game.resume();
                }
            },
            update: (deltaTime) => {
                if (window.game) {
                    window.game.update(deltaTime);
                }
            },
            render: (renderer) => {
                if (window.game) {
                    window.game.render();
                }
            },
            exit: () => {
                if (window.game) {
                    window.game.pause();
                }
            }
        });
        
        // Paused state
        this.addState('paused', {
            enter: () => {
                console.log('Entering paused state');
                this.showPauseMenu();
            },
            update: (deltaTime) => {
                this.updatePauseMenu(deltaTime);
            },
            render: (renderer) => {
                // Render game in background
                if (window.game) {
                    window.game.render();
                }
                this.renderPauseMenu(renderer);
            },
            exit: () => {
                this.hidePauseMenu();
            }
        });
        
        // Game over state
        this.addState('gameOver', {
            enter: () => {
                console.log('Entering game over state');
                this.showGameOverScreen();
            },
            update: (deltaTime) => {
                this.updateGameOverScreen(deltaTime);
            },
            render: (renderer) => {
                this.renderGameOverScreen(renderer);
            },
            exit: () => {
                this.hideGameOverScreen();
            }
        });
    }
    
    addState(name, stateObject) {
        this.states.set(name, stateObject);
    }
    
    setState(newState) {
        if (newState === this.currentState) return;
        
        const currentStateObj = this.states.get(this.currentState);
        const newStateObj = this.states.get(newState);
        
        if (!newStateObj) {
            console.warn(`State '${newState}' not found`);
            return;
        }
        
        // Exit current state
        if (currentStateObj && currentStateObj.exit) {
            currentStateObj.exit();
        }
        
        // Update state
        this.previousState = this.currentState;
        this.currentState = newState;
        
        // Enter new state
        if (newStateObj.enter) {
            newStateObj.enter();
        }
        
        // Trigger transition callbacks
        const transitionKey = `${this.previousState}->${newState}`;
        const callback = this.transitionCallbacks.get(transitionKey);
        if (callback) {
            callback();
        }
        
        console.log(`State transition: ${this.previousState} -> ${newState}`);
    }
    
    getCurrentState() {
        return this.currentState;
    }
    
    getPreviousState() {
        return this.previousState;
    }
    
    update(deltaTime) {
        const stateObj = this.states.get(this.currentState);
        if (stateObj && stateObj.update) {
            stateObj.update(deltaTime);
        }
    }
    
    render(renderer) {
        const stateObj = this.states.get(this.currentState);
        if (stateObj && stateObj.render) {
            stateObj.render(renderer);
        }
    }
    
    addTransitionCallback(fromState, toState, callback) {
        const key = `${fromState}->${toState}`;
        this.transitionCallbacks.set(key, callback);
    }
    
    // Loading screen methods
    showLoadingScreen() {
        const loadingDiv = document.getElementById('loading-screen');
        if (loadingDiv) {
            loadingDiv.style.display = 'flex';
        }
    }
    
    hideLoadingScreen() {
        const loadingDiv = document.getElementById('loading-screen');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }
    
    updateLoadingScreen(deltaTime) {
        // Check if loading is complete
        let totalProgress = 0;
        let progressCount = 0;
        
        if (window.assetLoader) {
            totalProgress += window.assetLoader.getLoadingProgress();
            progressCount++;
        }
        
        if (window.soundManager) {
            totalProgress += window.soundManager.getLoadingProgress();
            progressCount++;
        }
        
        const averageProgress = progressCount > 0 ? totalProgress / progressCount : 1;
        
        // Update progress bar
        const progressBar = document.getElementById('loading-progress');
        if (progressBar) {
            progressBar.style.width = `${averageProgress * 100}%`;
        }
        
        // Transition to menu when loading is complete
        if (averageProgress >= 1.0) {
            setTimeout(() => {
                this.setState('menu');
            }, 500); // Small delay for visual feedback
        }
    }
    
    renderLoadingScreen(renderer) {
        // Loading screen is handled by HTML/CSS
    }
    
    // Main menu methods
    showMainMenu() {
        const menuDiv = document.getElementById('main-menu');
        if (menuDiv) {
            menuDiv.style.display = 'flex';
        }
    }
    
    hideMainMenu() {
        const menuDiv = document.getElementById('main-menu');
        if (menuDiv) {
            menuDiv.style.display = 'none';
        }
    }
    
    updateMainMenu(deltaTime) {
        // Handle menu input
        if (window.inputManager && window.inputManager.isKeyPressed('Enter')) {
            this.setState('playing');
        }
    }
    
    renderMainMenu(renderer) {
        // Main menu is handled by HTML/CSS
    }
    
    // Pause menu methods
    showPauseMenu() {
        const pauseDiv = document.getElementById('pause-menu');
        if (pauseDiv) {
            pauseDiv.style.display = 'flex';
        }
    }
    
    hidePauseMenu() {
        const pauseDiv = document.getElementById('pause-menu');
        if (pauseDiv) {
            pauseDiv.style.display = 'none';
        }
    }
    
    updatePauseMenu(deltaTime) {
        // Handle pause menu input
        if (window.inputManager && window.inputManager.isKeyPressed('Escape')) {
            this.setState('playing');
        }
    }
    
    renderPauseMenu(renderer) {
        // Pause menu is handled by HTML/CSS
    }
    
    // Game over screen methods
    showGameOverScreen() {
        const gameOverDiv = document.getElementById('game-over-screen');
        if (gameOverDiv) {
            gameOverDiv.style.display = 'flex';
        }
    }
    
    hideGameOverScreen() {
        const gameOverDiv = document.getElementById('game-over-screen');
        if (gameOverDiv) {
            gameOverDiv.style.display = 'none';
        }
    }
    
    updateGameOverScreen(deltaTime) {
        // Handle game over input
        if (window.inputManager && (window.inputManager.isKeyPressed('Enter') || window.inputManager.isKeyPressed('Space'))) {
            this.setState('menu');
        }
    }
    
    renderGameOverScreen(renderer) {
        // Game over screen is handled by HTML/CSS
    }
    
    // Utility methods
    isInState(stateName) {
        return this.currentState === stateName;
    }
    
    canPause() {
        return this.currentState === 'playing';
    }
    
    togglePause() {
        if (this.currentState === 'playing') {
            this.setState('paused');
        } else if (this.currentState === 'paused') {
            this.setState('playing');
        }
    }
}

// Create global game state manager
window.gameStateManager = new GameStateManager();

// Export for use in other modules
window.GameStateManager = GameStateManager;
