/**
 * MAIN GAME ENTRY POINT - ENHANCED DUNGEON CRAWLER
 *
 * Initializes the game and starts the main game loop.
 * Handles window events and manages the overall game state.
 *
 * COMPREHENSIVE ENHANCEMENTS INCLUDED:
 * ✅ Performance optimizations (entity culling, object pooling, spatial partitioning)
 * ✅ Enhanced asset pipeline with loading screens and sprite animations
 * ✅ 7 enemy types with unique AI behaviors and special abilities
 * ✅ Expanded item system with rarities and special effects
 * ✅ Dungeon features (traps, treasure chests, locked doors, secret walls)
 * ✅ Player skill system with combat, defense, agility, and magic skills
 * ✅ Status effects system (poison, strength, speed, defense buffs)
 * ✅ Game state management (loading, menu, playing, paused, game over)
 * ✅ Sound effects and particle systems
 * ✅ Performance monitoring and debug information
 * ✅ Mobile-friendly controls and responsive design
 * ✅ Comprehensive error handling and system verification
 */

class Game {
    constructor() {
        // Core systems
        this.canvas = null;
        this.renderer = null;
        this.running = false;
        this.paused = false;
        
        // Timing
        this.lastTime = 0;
        this.deltaTime = 0;
        this.fps = 0;
        this.fpsCounter = 0;
        this.fpsTimer = 0;
        
        // Game state
        this.gameState = 'loading'; // loading, menu, playing, paused, gameover
        
        // Game objects
        this.player = null;
        this.enemies = [];
        this.items = [];
        this.dungeon = null;
        
        // Systems
        this.combat = null;
        this.spatialGrid = null;
        this.dungeonFeatures = null;

        // Performance monitoring
        this.performanceStats = {
            entityCount: 0,
            renderTime: 0,
            updateTime: 0,
            memoryUsage: 0
        };
        
        // Initialize the game
        this.init();
    }
    
    async init() {
        try {
            console.log('Initializing Dungeon Crawler Game...');
            
            // Get canvas and setup renderer
            this.canvas = document.getElementById('gameCanvas');
            if (!this.canvas) {
                throw new Error('Canvas element not found');
            }
            
            this.renderer = new Renderer(this.canvas);
            
            // Setup window events
            this.setupWindowEvents();
            
            // Start with loading state
            if (window.gameStateManager) {
                window.gameStateManager.setState('loading');
            }

            // Initialize game systems
            this.initializeSystems();

            // Load assets
            await this.loadAssets();

            // Create initial game objects
            this.createGameObjects();

            // Start the game loop
            this.gameState = 'playing';
            this.running = true;
            this.lastTime = performance.now();

            // Transition to menu state after loading
            if (window.gameStateManager) {
                window.gameStateManager.setState('menu');
            }

            console.log('Game initialized successfully!');
            this.gameLoop();
            
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showError('Failed to initialize game: ' + error.message);
        }
    }

    async loadAssets() {
        console.log('Loading assets...');

        // Load assets through asset loader
        if (window.assetLoader) {
            await window.assetLoader.loadManifest();
            await window.assetLoader.loadPreloadAssets();
        }

        // Load sounds through sound manager
        if (window.soundManager) {
            await window.soundManager.loadAllSounds();
        }

        console.log('Assets loaded successfully!');
    }

    setupWindowEvents() {
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Handle visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
        
        // Handle focus events
        window.addEventListener('blur', () => this.pause());
        window.addEventListener('focus', () => this.resume());

        // Setup game state UI event handlers
        this.setupGameStateHandlers();
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    setupGameStateHandlers() {
        // Main menu buttons
        const startGameBtn = document.getElementById('start-game-btn');
        if (startGameBtn) {
            startGameBtn.addEventListener('click', () => {
                if (window.gameStateManager) {
                    window.gameStateManager.setState('playing');
                }
            });
        }

        // Pause menu buttons
        const resumeBtn = document.getElementById('resume-btn');
        if (resumeBtn) {
            resumeBtn.addEventListener('click', () => {
                if (window.gameStateManager) {
                    window.gameStateManager.setState('playing');
                }
            });
        }

        const restartBtn = document.getElementById('restart-btn');
        if (restartBtn) {
            restartBtn.addEventListener('click', () => {
                this.restart();
            });
        }

        const mainMenuBtn = document.getElementById('main-menu-btn');
        if (mainMenuBtn) {
            mainMenuBtn.addEventListener('click', () => {
                if (window.gameStateManager) {
                    window.gameStateManager.setState('menu');
                }
            });
        }

        // Game over buttons
        const playAgainBtn = document.getElementById('play-again-btn');
        if (playAgainBtn) {
            playAgainBtn.addEventListener('click', () => {
                this.restart();
            });
        }

        const backToMenuBtn = document.getElementById('back-to-menu-btn');
        if (backToMenuBtn) {
            backToMenuBtn.addEventListener('click', () => {
                if (window.gameStateManager) {
                    window.gameStateManager.setState('menu');
                }
            });
        }
    }

    restart() {
        console.log('Restarting game...');

        // Reset game state
        this.gameState = 'playing';
        this.paused = false;

        // Clear existing entities
        this.enemies = [];
        this.items = [];
        this.player = null;

        // Regenerate game world
        this.createGameObjects();

        // Transition to playing state
        if (window.gameStateManager) {
            window.gameStateManager.setState('playing');
        }

        console.log('Game restarted successfully!');
    }

    verifySystemIntegrity() {
        const systems = [
            { name: 'Renderer', obj: this.renderer },
            { name: 'Combat', obj: this.combat },
            { name: 'Spatial Grid', obj: this.spatialGrid },
            { name: 'Dungeon Features', obj: this.dungeonFeatures },
            { name: 'Dungeon Generator', obj: this.dungeonGenerator },
            { name: 'Input Manager', obj: window.inputManager },
            { name: 'Sound Manager', obj: window.soundManager },
            { name: 'Asset Loader', obj: window.assetLoader },
            { name: 'Particle Pool', obj: window.particlePool },
            { name: 'Damage Text Pool', obj: window.damageTextPool },
            { name: 'Game State Manager', obj: window.gameStateManager }
        ];

        let allSystemsOk = true;

        systems.forEach(system => {
            if (!system.obj) {
                console.warn(`⚠️ System not initialized: ${system.name}`);
                allSystemsOk = false;
            } else {
                console.log(`✅ System OK: ${system.name}`);
            }
        });

        // Check player skills system
        if (this.player && this.player.skills) {
            console.log('✅ Player skills system initialized');
        } else {
            console.warn('⚠️ Player skills system not found');
            allSystemsOk = false;
        }

        // Check dungeon features
        if (this.dungeonFeatures && this.dungeonFeatures.features) {
            console.log(`✅ Dungeon features: ${this.dungeonFeatures.features.size} features loaded`);
        } else {
            console.warn('⚠️ Dungeon features not properly initialized');
            allSystemsOk = false;
        }

        if (allSystemsOk) {
            console.log('🎉 All game systems verified and working correctly!');
        } else {
            console.warn('⚠️ Some systems may not be working correctly. Check console for details.');
        }

        return allSystemsOk;
    }

    initializeSystems() {
        // Initialize combat system
        this.combat = new CombatSystem();

        // Initialize dungeon generator
        this.dungeonGenerator = new DungeonGenerator();

        // Initialize spatial grid for collision optimization
        const dungeonWidth = CONFIG.DUNGEON.FLOOR_WIDTH * CONFIG.GAME.SCALED_TILE_SIZE;
        const dungeonHeight = CONFIG.DUNGEON.FLOOR_HEIGHT * CONFIG.GAME.SCALED_TILE_SIZE;
        const cellSize = CONFIG.GAME.SCALED_TILE_SIZE * 2; // 2x2 tile cells
        this.spatialGrid = new SpatialGrid(dungeonWidth, dungeonHeight, cellSize);

        // Initialize dungeon features system
        this.dungeonFeatures = new DungeonFeatures(this);

        console.log('Game systems initialized');

        // Verify all systems are properly initialized
        this.verifySystemIntegrity();
    }
    
    createGameObjects() {
        // Create player
        this.player = new Player(400, 300);
        
        // Generate initial dungeon
        this.dungeon = this.dungeonGenerator.generate();
        
        // Place player at dungeon start
        const startRoom = this.dungeon.rooms[0];
        if (startRoom) {
            this.player.setPosition(
                startRoom.centerX * CONFIG.GAME.SCALED_TILE_SIZE,
                startRoom.centerY * CONFIG.GAME.SCALED_TILE_SIZE
            );
        }
        
        // Spawn initial enemies
        this.spawnEnemies();

        // Spawn initial items
        this.spawnItems();

        console.log('Game objects created');
    }
    
    spawnEnemies() {
        // Spawn enemies in rooms (excluding the first room where player starts)
        for (let i = 1; i < this.dungeon.rooms.length; i++) {
            const room = this.dungeon.rooms[i];
            
            if (Math.random() < CONFIG.DUNGEON.ENEMY_SPAWN_CHANCE) {
                const enemyCount = Utils.randomInt(
                    CONFIG.DUNGEON.ENEMIES_PER_ROOM.MIN,
                    CONFIG.DUNGEON.ENEMIES_PER_ROOM.MAX
                );
                
                for (let j = 0; j < enemyCount; j++) {
                    // Random position within room
                    const x = Utils.randomInt(room.x + 1, room.x + room.width - 2) * CONFIG.GAME.SCALED_TILE_SIZE;
                    const y = Utils.randomInt(room.y + 1, room.y + room.height - 2) * CONFIG.GAME.SCALED_TILE_SIZE;
                    
                    // Random enemy type based on spawn rates
                    const enemyType = this.getRandomEnemyType();
                    const enemy = new Enemy(x, y, enemyType);
                    this.enemies.push(enemy);

                    console.log(`Spawned ${enemyType} at (${x}, ${y}) in room ${i}`);
                }
            }
        }

        console.log(`Total enemies spawned: ${this.enemies.length}`);
    }

    spawnItems() {
        // Spawn items in rooms (excluding the first room where player starts)
        for (let i = 1; i < this.dungeon.rooms.length; i++) {
            const room = this.dungeon.rooms[i];

            if (Math.random() < CONFIG.DUNGEON.ITEM_SPAWN_CHANCE) {
                const itemCount = Utils.randomInt(
                    CONFIG.DUNGEON.ITEMS_PER_ROOM.MIN,
                    CONFIG.DUNGEON.ITEMS_PER_ROOM.MAX
                );

                for (let j = 0; j < itemCount; j++) {
                    // Try to find a valid spawn position
                    let spawnAttempts = 0;
                    let validPosition = false;
                    let x, y;

                    while (!validPosition && spawnAttempts < 10) {
                        // Random position within room
                        x = Utils.randomInt(room.x + 1, room.x + room.width - 2) * CONFIG.GAME.SCALED_TILE_SIZE;
                        y = Utils.randomInt(room.y + 1, room.y + room.height - 2) * CONFIG.GAME.SCALED_TILE_SIZE;

                        // Check if position is valid (not on wall, not overlapping with enemies/items)
                        validPosition = this.isValidSpawnPosition(x, y);
                        spawnAttempts++;
                    }

                    if (validPosition) {
                        // Random item type
                        const itemInfo = this.getRandomItem();
                        const item = new Item(x, y, itemInfo.type, itemInfo.category);
                        this.items.push(item);

                        console.log(`Spawned ${itemInfo.category}.${itemInfo.type} at (${x}, ${y}) in room ${i}`);
                    } else {
                        console.log(`Failed to find valid spawn position for item in room ${i}`);
                    }
                }
            }
        }

        console.log(`Total items spawned: ${this.items.length}`);

        // Generate dungeon features
        this.generateDungeonFeatures();
    }

    generateDungeonFeatures() {
        if (!this.dungeonFeatures || !this.dungeon) return;

        // Generate features in rooms and corridors
        for (let x = 0; x < this.dungeon.width; x++) {
            for (let y = 0; y < this.dungeon.height; y++) {
                const tile = this.dungeon.getTile(x, y);

                if (tile === CONFIG.DUNGEON.TILE_TYPES.FLOOR) {
                    // Check if this is a good spot for features
                    if (this.isValidFeaturePosition(x, y)) {
                        this.tryPlaceFeature(x, y);
                    }
                }
            }
        }

        console.log('Dungeon features generated');
    }

    isValidFeaturePosition(x, y) {
        // Don't place features too close to player start
        const startRoom = this.dungeon.rooms[0];
        if (startRoom) {
            const distToStart = Math.abs(x - startRoom.centerX) + Math.abs(y - startRoom.centerY);
            if (distToStart < 3) return false;
        }

        // Don't place features adjacent to walls (need clear space)
        const adjacentWalls = [
            this.dungeon.getTile(x-1, y),
            this.dungeon.getTile(x+1, y),
            this.dungeon.getTile(x, y-1),
            this.dungeon.getTile(x, y+1)
        ].filter(tile => tile === CONFIG.DUNGEON.TILE_TYPES.WALL).length;

        return adjacentWalls <= 2; // Allow some walls but not surrounded
    }

    tryPlaceFeature(x, y) {
        const random = Math.random();

        // Treasure chests (in rooms)
        if (random < CONFIG.DUNGEON.TREASURE_CHEST_CHANCE && this.isInRoom(x, y)) {
            this.dungeonFeatures.addFeature(x, y, 'treasure_chest', {
                itemCount: Utils.random(1, 3),
                rareChance: 0.3
            });
            this.dungeon.setTile(x, y, CONFIG.DUNGEON.TILE_TYPES.TREASURE_CHEST);
        }
        // Traps (in corridors mainly)
        else if (random < CONFIG.DUNGEON.TRAP_CHANCE && !this.isInRoom(x, y)) {
            const trapType = Utils.randomChoice(['spike', 'poison', 'explosion']);
            this.dungeonFeatures.addFeature(x, y, 'trap', {
                trapType: trapType
            });
            this.dungeon.setTile(x, y, CONFIG.DUNGEON.TILE_TYPES.TRAP);
        }
        // Secret walls (rare)
        else if (random < CONFIG.DUNGEON.SECRET_WALL_CHANCE) {
            this.dungeonFeatures.addFeature(x, y, 'secret_wall');
            this.dungeon.setTile(x, y, CONFIG.DUNGEON.TILE_TYPES.SECRET_WALL);
        }
    }

    isInRoom(x, y) {
        return this.dungeon.rooms.some(room =>
            x >= room.x && x < room.x + room.width &&
            y >= room.y && y < room.y + room.height
        );
    }
    
    getRandomEnemyType() {
        const rand = Math.random() * 100;
        let cumulative = 0;
        
        for (const [type, rate] of Object.entries(CONFIG.ENEMIES.SPAWN_RATES)) {
            cumulative += rate;
            if (rand <= cumulative) {
                return type.toLowerCase();
            }
        }
        
        return 'goblin'; // Default fallback
    }

    getRandomItem() {
        // Randomly select item category
        const categories = ['weapons', 'armor', 'consumables'];
        const weights = [30, 25, 45]; // Consumables more common

        let totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        let random = Math.random() * totalWeight;

        let selectedCategory = categories[0];
        for (let i = 0; i < categories.length; i++) {
            if (random < weights[i]) {
                selectedCategory = categories[i];
                break;
            }
            random -= weights[i];
        }

        // Get items from selected category
        const categoryItems = CONFIG.ITEMS[selectedCategory.toUpperCase()];
        const itemKeys = Object.keys(categoryItems);

        // Select item based on drop rates
        const availableItems = itemKeys.filter(key => {
            const item = categoryItems[key];
            return Math.random() * 100 < item.DROP_RATE;
        });

        if (availableItems.length === 0) {
            // Fallback to first item if no items pass drop rate check
            return {
                category: selectedCategory,
                type: itemKeys[0].toLowerCase()
            };
        }

        const selectedItem = Utils.randomChoice(availableItems);
        return {
            category: selectedCategory,
            type: selectedItem.toLowerCase()
        };
    }

    isValidSpawnPosition(x, y) {
        const tileSize = CONFIG.GAME.SCALED_TILE_SIZE;
        const tileX = Math.floor(x / tileSize);
        const tileY = Math.floor(y / tileSize);

        // Check if position is on a floor tile
        if (this.dungeon.getTile(tileX, tileY) !== CONFIG.DUNGEON.TILE_TYPES.FLOOR) {
            return false;
        }

        // Check for overlap with enemies
        for (const enemy of this.enemies) {
            const distance = Utils.distance(x, y, enemy.x, enemy.y);
            if (distance < tileSize) {
                return false;
            }
        }

        // Check for overlap with existing items
        for (const item of this.items) {
            const distance = Utils.distance(x, y, item.x, item.y);
            if (distance < tileSize / 2) {
                return false;
            }
        }

        // Check distance from player spawn
        if (this.player) {
            const distance = Utils.distance(x, y, this.player.x, this.player.y);
            if (distance < tileSize * 2) {
                return false;
            }
        }

        return true;
    }
    
    gameLoop() {
        if (!this.running) return;
        
        const currentTime = performance.now();
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Cap delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, 1000 / 30); // Max 30 FPS minimum
        
        // Update FPS counter
        this.updateFPS();
        
        // Update performance stats
        this.updatePerformanceStats();

        // Update game state manager
        if (window.gameStateManager) {
            window.gameStateManager.update(this.deltaTime);
        }

        // Handle pause input
        if (window.inputManager && window.inputManager.isPauseToggled()) {
            if (window.gameStateManager) {
                window.gameStateManager.togglePause();
            } else {
                this.togglePause();
            }
        }

        // Update game if not paused
        if (!this.paused && this.gameState === 'playing') {
            const updateStart = performance.now();
            this.update(this.deltaTime);
            this.performanceStats.updateTime = performance.now() - updateStart;
        }

        // Render through state manager or directly
        const renderStart = performance.now();
        if (window.gameStateManager) {
            window.gameStateManager.render(this.renderer);
        } else {
            this.render();
        }
        this.performanceStats.renderTime = performance.now() - renderStart;
        
        // Continue game loop
        requestAnimationFrame(() => this.gameLoop());
    }
    
    update(deltaTime) {
        // Update input manager
        if (window.inputManager) {
            window.inputManager.update();
        }

        // Handle pause input
        if (window.inputManager && window.inputManager.isKeyPressed('Escape') && window.gameStateManager) {
            if (window.gameStateManager.canPause()) {
                window.gameStateManager.togglePause();
                return; // Don't update game when pausing
            }
        }

        // Only update game if in playing state
        if (window.gameStateManager && !window.gameStateManager.isInState('playing')) {
            return;
        }

        // Clear spatial grid for this frame
        if (this.spatialGrid) {
            this.spatialGrid.clear();
        }

        // Update player
        if (this.player && this.player.alive) {
            this.player.update(deltaTime);

            // Add player to spatial grid
            if (this.spatialGrid) {
                this.spatialGrid.insert(this.player);
            }

            // Update camera to follow player
            this.renderer.updateCamera(
                this.player.x + this.player.width / 2,
                this.player.y + this.player.height / 2
            );
        } else if (this.player && !this.player.alive) {
            // Player died - transition to game over
            if (window.gameStateManager) {
                window.gameStateManager.setState('gameOver');
            }
        }
        
        // Update enemies with culling
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];

            // Remove dead enemies first
            if (!enemy.alive) {
                this.enemies.splice(i, 1);
                continue;
            }

            // Check if enemy is within update distance
            const distanceToPlayer = this.player ? enemy.distanceTo(this.player) : Infinity;
            const shouldUpdate = distanceToPlayer <= CONFIG.PERFORMANCE.ENTITY_CULL_DISTANCE;

            if (shouldUpdate) {
                enemy.update(deltaTime, this.player, this.dungeon);
            } else {
                // For distant enemies, only update position slightly to prevent them from being completely static
                enemy.applyFriction(0.95);
                // Reduce AI update frequency for distant enemies
                if (Math.random() < 0.1) { // 10% chance to update AI
                    enemy.updateAI(deltaTime, this.player);
                }
            }

            // Add enemy to spatial grid
            if (this.spatialGrid) {
                this.spatialGrid.insert(enemy);
            }
        }
        
        // Update items and check for pickup with culling
        for (let i = this.items.length - 1; i >= 0; i--) {
            const item = this.items[i];

            // Remove dead items first
            if (!item.alive) {
                this.items.splice(i, 1);
                continue;
            }

            // Check distance to player for culling
            const distanceToPlayer = this.player ? item.distanceTo(this.player) : Infinity;
            const shouldUpdate = distanceToPlayer <= CONFIG.PERFORMANCE.ENTITY_CULL_DISTANCE;

            if (shouldUpdate) {
                item.update(deltaTime);

                // Check for player pickup only if close enough
                if (this.player && this.player.isCollidingWith(item)) {
                    item.onPickup(this.player);
                    this.items.splice(i, 1);
                    continue;
                }
            }
            // Items don't need updates when far away - they're static

            // Add item to spatial grid
            if (this.spatialGrid) {
                this.spatialGrid.insert(item);
            }
        }

        // Update combat system
        this.combat.update(deltaTime, this.player, this.enemies);

        // Update dungeon features
        if (this.dungeonFeatures) {
            this.dungeonFeatures.update(deltaTime);
            this.dungeonFeatures.handlePlayerInteraction(this.player);
        }

        // Update renderer effects
        this.renderer.updateDamageTexts(deltaTime);
        
        // Check win/lose conditions
        this.checkGameState();
    }
    
    render() {
        // Clear screen
        this.renderer.clear();
        
        // Render dungeon
        if (this.dungeon) {
            this.dungeon.render(this.renderer);
        }
        
        // Render items with culling
        this.items.forEach(item => {
            const distanceToPlayer = this.player ? item.distanceTo(this.player) : 0;
            if (distanceToPlayer <= CONFIG.PERFORMANCE.RENDER_CULL_DISTANCE) {
                item.render(this.renderer);
            }
        });

        // Render enemies with culling
        this.enemies.forEach(enemy => {
            const distanceToPlayer = this.player ? enemy.distanceTo(this.player) : 0;
            if (distanceToPlayer <= CONFIG.PERFORMANCE.RENDER_CULL_DISTANCE) {
                enemy.render(this.renderer);
            }
        });
        
        // Render player
        if (this.player) {
            this.player.render(this.renderer);
        }
        
        // Render effects
        this.renderer.drawDamageTexts();

        // Render combat effects
        if (this.combat) {
            this.combat.renderAttackEffects(this.renderer);
        }

        // Render debug info
        this.renderer.drawDebugInfo(this.player, this.enemies, this.fps);
        
        // Render hitboxes if enabled
        if (CONFIG.GAME.SHOW_HITBOXES) {
            const allEntities = [this.player, ...this.enemies, ...this.items].filter(e => e);
            this.renderer.drawHitboxes(allEntities);
        }
        
        // Render spatial grid debug info
        if (this.spatialGrid && CONFIG.GAME.DEBUG_MODE) {
            this.spatialGrid.render(this.renderer);
        }

        // Render dungeon features
        if (this.dungeonFeatures) {
            this.dungeonFeatures.render(this.renderer);
        }

        // Render mobile controls
        this.renderer.drawVirtualJoystick();

        // Render enhanced overlay UI
        if (CONFIG.GAME.DEBUG_MODE) {
            this.renderer.drawEnhancedPlayerUI(this.player);
        }

        // Render HTML UI
        this.renderUI();
    }
    
    renderUI() {
        if (!this.player) return;
        
        // Update health bar
        const healthBar = document.getElementById('healthBar');
        const healthText = document.getElementById('healthText');
        if (healthBar && healthText) {
            const healthPercent = (this.player.health / this.player.maxHealth) * 100;
            healthBar.style.width = healthPercent + '%';
            healthText.textContent = `${this.player.health}/${this.player.maxHealth}`;
        }
        
        // Update mana bar (if implemented)
        const manaBar = document.getElementById('manaBar');
        const manaText = document.getElementById('manaText');
        if (manaBar && manaText) {
            const manaPercent = (this.player.mana / this.player.maxMana) * 100;
            manaBar.style.width = manaPercent + '%';
            manaText.textContent = `${this.player.mana}/${this.player.maxMana}`;
        }
        
        // Update experience bar (if implemented)
        const expBar = document.getElementById('expBar');
        const expText = document.getElementById('expText');
        if (expBar && expText) {
            const expPercent = (this.player.experience / this.player.experienceToNext) * 100;
            expBar.style.width = expPercent + '%';
            expText.textContent = `${this.player.experience}/${this.player.experienceToNext}`;
        }
    }
    
    updateFPS() {
        this.fpsCounter++;
        this.fpsTimer += this.deltaTime;

        if (this.fpsTimer >= 1000) {
            this.fps = this.fpsCounter;
            this.fpsCounter = 0;
            this.fpsTimer = 0;
        }
    }

    updatePerformanceStats() {
        // Count entities
        this.performanceStats.entityCount =
            (this.player ? 1 : 0) +
            this.enemies.length +
            this.items.length;

        // Memory usage (if available)
        if (performance.memory) {
            this.performanceStats.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
        }
    }
    
    checkGameState() {
        // Check if player is dead
        if (this.player && !this.player.alive) {
            this.gameState = 'gameover';
            this.showGameOver();
        }
        
        // Check if all enemies are defeated (simple win condition)
        if (this.enemies.length === 0 && this.gameState === 'playing') {
            this.gameState = 'victory';
            this.showVictory();
        }
    }
    
    pause() {
        this.paused = true;
        console.log('Game paused');
    }
    
    resume() {
        this.paused = false;
        this.lastTime = performance.now(); // Reset timing to prevent large delta
        console.log('Game resumed');
    }
    
    togglePause() {
        if (this.paused) {
            this.resume();
        } else {
            this.pause();
        }
    }
    
    handleResize() {
        // Handle responsive canvas sizing if needed
        const container = document.getElementById('gameContainer');
        if (container) {
            // Could implement responsive scaling here
        }
    }
    
    showError(message) {
        console.error(message);
        // Could show error UI here
    }
    
    showGameOver() {
        console.log('Game Over!');
        alert('Game Over! Press OK to restart.');
        this.restart();
    }

    showVictory() {
        console.log('Victory!');
        alert(`Victory! You defeated all enemies!\nFinal Level: ${this.player.level}\nItems Collected: ${this.player.inventory.weapons + this.player.inventory.armor + this.player.inventory.consumables}\n\nPress OK to play again.`);
        this.restart();
    }
    

}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.game = new Game();
});
