/**
 * PLAYER CLASS
 * 
 * Handles player character logic, movement, stats, and abilities.
 */

class Player extends Entity {
    constructor(x, y) {
        super(x, y, CONFIG.GAME.SCALED_TILE_SIZE, CONFIG.GAME.SCALED_TILE_SIZE);
        
        // Player stats from config
        this.health = CONFIG.PLAYER.BASE_HEALTH;
        this.maxHealth = CONFIG.PLAYER.BASE_HEALTH;
        this.mana = CONFIG.PLAYER.BASE_MANA;
        this.maxMana = CONFIG.PLAYER.BASE_MANA;
        this.attack = CONFIG.PLAYER.BASE_ATTACK;
        this.defense = CONFIG.PLAYER.BASE_DEFENSE;
        this.speed = CONFIG.PLAYER.BASE_SPEED;
        
        // Level and experience
        this.level = CONFIG.PLAYER.STARTING_LEVEL;
        this.experience = 0;
        this.experienceToNext = CONFIG.PLAYER.EXP_TO_NEXT_LEVEL;
        
        // Combat
        this.attackCooldown = 0;
        this.lastAttackTime = 0;
        
        // Movement
        this.acceleration = CONFIG.PLAYER.ACCELERATION;
        this.friction = CONFIG.PLAYER.FRICTION;
        
        // Visual
        this.sprite = 'player';
        this.type = 'player';
        
        // Hitbox adjustment
        this.hitbox = {
            offsetX: (this.width - CONFIG.PLAYER.HITBOX_WIDTH) / 2,
            offsetY: (this.height - CONFIG.PLAYER.HITBOX_HEIGHT) / 2,
            width: CONFIG.PLAYER.HITBOX_WIDTH,
            height: CONFIG.PLAYER.HITBOX_HEIGHT
        };

        // Simple inventory tracking
        this.inventory = {
            weapons: 0,
            armor: 0,
            consumables: 0
        };

        // Status effects
        this.statusEffects = new Map();
        this.invulnerable = false;
        this.invulnerabilityTimer = 0;

        // Temporary stat bonuses
        this.tempAttackBonus = 0;
        this.tempDefenseBonus = 0;
        this.tempSpeedBonus = 0;

        // Skills and progression
        this.skills = {
            combat: 0,      // Increases attack damage
            defense: 0,     // Increases defense and health
            agility: 0,     // Increases speed and dodge chance
            magic: 0        // Increases mana and spell effectiveness
        };
        this.skillPoints = 0;
        this.totalKills = 0;
        this.totalDamageDealt = 0;
        this.totalDamageTaken = 0;
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        
        // Handle movement input
        this.handleMovement(deltaTime);
        
        // Handle attack input
        this.handleAttack();
        
        // Update cooldowns
        this.updateCooldowns(deltaTime);

        // Update status effects
        this.updateStatusEffects(deltaTime);

        // Apply friction
        this.applyFriction(this.friction);
    }
    
    handleMovement(deltaTime) {
        const movement = window.inputManager ? window.inputManager.getMovement() : { x: 0, y: 0 };
        
        if (movement.x !== 0 || movement.y !== 0) {
            // Apply acceleration
            const accel = this.acceleration * (deltaTime / 1000);
            
            this.velocityX += movement.x * accel;
            this.velocityY += movement.y * accel;
            
            // Cap velocity to max speed (including bonuses)
            const effectiveSpeed = this.getEffectiveSpeed();
            const currentSpeed = Math.sqrt(this.velocityX * this.velocityX + this.velocityY * this.velocityY);
            if (currentSpeed > effectiveSpeed) {
                this.velocityX = (this.velocityX / currentSpeed) * effectiveSpeed;
                this.velocityY = (this.velocityY / currentSpeed) * effectiveSpeed;
            }
        }
    }
    
    handleAttack() {
        if (window.inputManager && window.inputManager.isAttacking() && this.canAttack()) {
            this.performAttack();
            // Clear the attack action after processing
            window.inputManager.clearAttackAction();
        }
    }
    
    canAttack() {
        return Date.now() - this.lastAttackTime >= CONFIG.PLAYER.ATTACK_COOLDOWN;
    }
    
    performAttack() {
        this.lastAttackTime = Date.now();

        // Play attack sound effect
        if (window.soundManager) {
            window.soundManager.playPlayerAttack();
        }

        // Get attack direction from mouse position
        const mousePos = window.inputManager ? window.inputManager.getMouseWorldPosition() : { x: this.x, y: this.y };
        const playerCenter = this.getCenter();

        const attackAngle = Utils.angle(playerCenter.x, playerCenter.y, mousePos.x, mousePos.y);

        // Attack originates from player center, extends in direction of mouse
        const attackRange = CONFIG.PLAYER.ATTACK_RANGE;
        const attackX = playerCenter.x + Math.cos(attackAngle) * (attackRange * 0.5);
        const attackY = playerCenter.y + Math.sin(attackAngle) * (attackRange * 0.5);

        // Store attack info for combat system
        this.currentAttack = {
            x: attackX,
            y: attackY,
            range: attackRange,
            damage: this.attack,
            angle: attackAngle,
            active: true
        };

        console.log(`Player attacks at angle ${Utils.toDegrees(attackAngle).toFixed(1)}° towards (${mousePos.x.toFixed(1)}, ${mousePos.y.toFixed(1)})`);
    }
    
    updateCooldowns(deltaTime) {
        if (this.attackCooldown > 0) {
            this.attackCooldown -= deltaTime;
        }
    }
    
    gainExperience(amount) {
        this.experience += amount;
        
        // Check for level up
        while (this.experience >= this.experienceToNext) {
            this.levelUp();
        }
    }
    
    levelUp() {
        this.level++;
        this.experience -= this.experienceToNext;
        
        // Increase stats
        this.maxHealth += CONFIG.PLAYER.HEALTH_PER_LEVEL;
        this.maxMana += CONFIG.PLAYER.MANA_PER_LEVEL;
        this.attack += CONFIG.PLAYER.ATTACK_PER_LEVEL;
        this.defense += CONFIG.PLAYER.DEFENSE_PER_LEVEL;
        
        // Restore health and mana
        this.health = this.maxHealth;
        this.mana = this.maxMana;
        
        // Calculate next level requirement
        this.experienceToNext = Math.floor(CONFIG.PLAYER.EXP_TO_NEXT_LEVEL * Math.pow(CONFIG.PLAYER.EXP_SCALING_FACTOR, this.level - 1));

        // Award skill points
        this.skillPoints += 2;

        console.log(`Level up! Now level ${this.level} - ${this.skillPoints} skill points available`);

        // Play level up sound effect
        if (window.soundManager) {
            window.soundManager.playLevelUp();
        }

        // Visual feedback
        if (window.renderer) {
            renderer.addDamageText(this.x + this.width / 2, this.y, 'LEVEL UP!', true);
        }

        // Add sparkle effect
        if (window.particlePool) {
            window.particlePool.createSparkles(
                this.x + this.width / 2,
                this.y + this.height / 2,
                12
            );
        }
    }

    // Status effect methods
    addStatusEffect(type, duration, value = 0) {
        this.statusEffects.set(type, {
            duration: duration,
            value: value,
            startTime: Date.now()
        });

        // Apply immediate effects
        switch (type) {
            case 'strength':
                this.tempAttackBonus += value;
                break;
            case 'defense':
                this.tempDefenseBonus += value;
                break;
            case 'speed':
                this.tempSpeedBonus += value;
                break;
            case 'poison':
                // Poison will be handled in update
                break;
        }

        console.log(`Applied ${type} status effect for ${duration}ms`);
    }

    removeStatusEffect(type) {
        const effect = this.statusEffects.get(type);
        if (!effect) return;

        // Remove effects
        switch (type) {
            case 'strength':
                this.tempAttackBonus -= effect.value;
                break;
            case 'defense':
                this.tempDefenseBonus -= effect.value;
                break;
            case 'speed':
                this.tempSpeedBonus -= effect.value;
                break;
        }

        this.statusEffects.delete(type);
        console.log(`Removed ${type} status effect`);
    }

    updateStatusEffects(deltaTime) {
        for (const [type, effect] of this.statusEffects.entries()) {
            effect.duration -= deltaTime;

            // Handle poison damage
            if (type === 'poison') {
                const timeSinceStart = Date.now() - effect.startTime;
                if (timeSinceStart % 1000 < deltaTime) { // Damage every second
                    this.takeDamage(effect.value);

                    // Visual poison effect
                    if (window.particlePool) {
                        window.particlePool.spawn(
                            this.x + this.width / 2,
                            this.y + this.height / 2,
                            Utils.random(-20, 20),
                            Utils.random(-30, -10),
                            1000,
                            2,
                            '#00ff00',
                            0,
                            0.98
                        );
                    }
                }
            }

            // Remove expired effects
            if (effect.duration <= 0) {
                this.removeStatusEffect(type);
            }
        }
    }

    // Get effective stats with bonuses
    getEffectiveAttack() {
        return this.attack + this.tempAttackBonus;
    }

    getEffectiveDefense() {
        return this.defense + this.tempDefenseBonus;
    }

    getEffectiveSpeed() {
        return this.speed + this.tempSpeedBonus;
    }

    hasStatusEffect(type) {
        return this.statusEffects.has(type);
    }

    // Skill system methods
    canUpgradeSkill(skillName) {
        return this.skillPoints > 0 && this.skills.hasOwnProperty(skillName);
    }

    upgradeSkill(skillName) {
        if (!this.canUpgradeSkill(skillName)) return false;

        this.skills[skillName]++;
        this.skillPoints--;

        // Apply skill bonuses
        this.applySkillBonuses();

        console.log(`Upgraded ${skillName} to level ${this.skills[skillName]}`);
        return true;
    }

    applySkillBonuses() {
        // Reset temporary bonuses from skills
        this.tempAttackBonus = Math.max(0, this.tempAttackBonus - this.getSkillBonus('combat', 'attack'));
        this.tempDefenseBonus = Math.max(0, this.tempDefenseBonus - this.getSkillBonus('defense', 'defense'));
        this.tempSpeedBonus = Math.max(0, this.tempSpeedBonus - this.getSkillBonus('agility', 'speed'));

        // Apply new skill bonuses
        this.tempAttackBonus += this.getSkillBonus('combat', 'attack');
        this.tempDefenseBonus += this.getSkillBonus('defense', 'defense');
        this.tempSpeedBonus += this.getSkillBonus('agility', 'speed');

        // Apply health bonus from defense skill
        const healthBonus = this.skills.defense * 5;
        this.maxHealth = CONFIG.PLAYER.BASE_HEALTH + (this.level - 1) * CONFIG.PLAYER.HEALTH_PER_LEVEL + healthBonus;

        // Apply mana bonus from magic skill
        const manaBonus = this.skills.magic * 3;
        this.maxMana = CONFIG.PLAYER.BASE_MANA + (this.level - 1) * CONFIG.PLAYER.MANA_PER_LEVEL + manaBonus;
    }

    getSkillBonus(skillName, statType) {
        const skillLevel = this.skills[skillName] || 0;

        switch (statType) {
            case 'attack':
                return skillLevel * 3; // +3 attack per combat skill level
            case 'defense':
                return skillLevel * 2; // +2 defense per defense skill level
            case 'speed':
                return skillLevel * 10; // +10 speed per agility skill level
            case 'mana':
                return skillLevel * 3; // +3 mana per magic skill level
            default:
                return 0;
        }
    }

    // Track combat statistics
    recordKill() {
        this.totalKills++;
    }

    recordDamageDealt(damage) {
        this.totalDamageDealt += damage;
    }

    recordDamageTaken(damage) {
        this.totalDamageTaken += damage;
    }

    // Override takeDamage to record stats
    takeDamage(damage) {
        const actualDamage = super.takeDamage ? super.takeDamage(damage) : damage;
        this.recordDamageTaken(actualDamage);
        return actualDamage;
    }

    // Get player statistics
    getStats() {
        return {
            level: this.level,
            experience: this.experience,
            experienceToNext: this.experienceToNext,
            health: this.health,
            maxHealth: this.maxHealth,
            mana: this.mana,
            maxMana: this.maxMana,
            attack: this.getEffectiveAttack(),
            defense: this.getEffectiveDefense(),
            speed: this.getEffectiveSpeed(),
            skills: { ...this.skills },
            skillPoints: this.skillPoints,
            totalKills: this.totalKills,
            totalDamageDealt: this.totalDamageDealt,
            totalDamageTaken: this.totalDamageTaken,
            statusEffects: Array.from(this.statusEffects.keys())
        };
    }
}

window.Player = Player;
